{"Language": "简体中文", "Translation": {"Video Script Configuration": "**视频脚本配置**", "Generate Video Script": "AI生成画面解说脚本", "Video Subject": "视频主题（给定一个关键词，:red[AI自动生成]视频文案）", "Script Language": "生成视频脚本的语言（一般情况AI会自动根据你输入的主题语言输出）", "Script Files": "脚本文件", "Generate Video Script and Keywords": "点击使用AI根据**主题**生成 【视频文案】 和 【视频关键词】", "Auto Detect": "自动检测", "Video Theme": "视频主题", "Generation Prompt": "自定义提示词", "Save Script": "保存脚本", "Crop Video": "裁剪视频", "Video File": "视频文件（:blue[1️⃣支持上传视频文件(限制2G) 2️⃣大文件建议直接导入 ./resource/videos 目录]）", "Plot Description": "剧情描述 (:blue[可从 https://www.tvmao.com/ 获取])", "Generate Video Keywords": "点击使用AI根据**文案**生成【视频关键】", "Please Enter the Video Subject": "请先填写视频文案", "Generating Video Script and Keywords": "AI正在生成视频文案和关键词...", "Generating Video Keywords": "AI正在生成视频关键词...", "Video Keywords": "视频关键词（:blue[对于长视频配合剧情描述效果更好]）", "Video Settings": "**视频设置**", "Video Concat Mode": "视频拼接模式", "Random": "随机拼接（推荐）", "Sequential": "顺序拼接", "Video Ratio": "视频比例", "Portrait": "竖屏 9:16（抖音视频）", "Landscape": "横屏 16:9（西瓜视频）", "Clip Duration": "视频片段最大时长(秒)（**不是视频总长度**，是指每个**合成片段**的长度）", "Number of Videos Generated Simultaneously": "同时生成视频数量", "Audio Settings": "**音频设置**", "Speech Synthesis": "朗读声音（:red[**与文案语言保持一致**。注意：V2版效果更好，但是需要API KEY]）", "Speech Region": "服务区域 (:red[必填，[点击获取](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Key": "API Key (:red[必填，密钥1 或 密钥2 均可 [点击获取](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Volume": "朗读音量（1.0表示100%）", "Speech Rate": "朗读速度（1.0表示1倍速）", "Male": "男性", "Female": "女性", "Background Music": "背景音乐", "No Background Music": "无背景音乐", "Random Background Music": "随机背景音乐", "Custom Background Music": "自定义背景音乐", "Custom Background Music File": "请输入自定义背景音乐的文件路径", "Background Music Volume": "背景音乐音量（0.2表示20%，背景声音不宜过高）", "Subtitle Settings": "**字幕设置**", "Enable Subtitles": "启用字幕（若取消勾选，下面的设置都将不生效）", "Font": "字幕字体", "Position": "字幕位置", "Top": "顶部", "Center": "中间", "Bottom": "底部（推荐）", "Custom": "自定义位置（70，表示离顶部70%的位置）", "Font Size": "字幕大小", "Font Color": "字幕颜色", "Stroke Color": "描边颜色", "Stroke Width": "描边粗细", "Generate Video": "生成视频", "Video Script and Subject Cannot Both Be Empty": "视频主题 和 视频文案，不能同时为空", "Generating Video": "正在生成视频，请稍候...", "Start Generating Video": "开始生成视频", "Video Generation Completed": "视频生成完成", "Video Generation Failed": "视频生成失败", "You can download the generated video from the following links": "你可以从以下链接下载生成的视频", "Basic Settings": "**基础设置** (:blue[点击展开])", "Pixabay API Key": "Pixabay API Key ([点击获取](https://pixabay.com/api/docs/#api_search_videos)) :red[可以不用配置，如果 Pexels 无法使用，再选择Pixabay]", "Video LLM Provider": "视频转录大模型", "LLM Provider": "大语言模型", "API Key": "API Key (:red[必填，需要到大模型提供商的后台申请])", "Base Url": "Base Url (可选)", "Model Name": "模型名称 (:blue[需要到大模型提供商的后台确认被授权的模型名称])", "Please Enter the LLM API Key": "请先填写大模型 **API Key**", "Please Enter the Pixabay API Key": "请先填写 **Pixabay API Key**", "Get Help": "一站式 AI 影视解说+自动化剪辑工具🎉🎉🎉\n\n有任何问题或建议，可以加入 **社区频道** 求助或讨论：https://github.com/linyqh/NarratoAI/wiki", "Video Source": "视频来源", "TikTok": "抖音 (TikTok 支持中，敬请期待)", "Bilibili": "哔哩哔哩 (Bilibili 支持中，敬请期待)", "Xiaohongshu": "小红书 (Xiaohongshu 支持中，敬请期待)", "Local file": "本地文件", "Play Voice": "试听语音合成", "Voice Example": "这是一段测试语音合成的示例文本", "Synthesizing Voice": "语音合成中，请稍候...", "TTS Provider": "语音合成提供商", "Hide Log": "隐藏日志", "Upload Local Files": "上传本地文件", "Video Check": "视频审查", "File Uploaded Successfully": "文件上传成功", "timestamp": "时间戳", "Picture description": "图片描述", "Narration": "视频文案", "Rebuild": "重新生成", "Load Video Script": "加载视频脚本", "Speech Pitch": "语调", "Please Select Script File": "请选择脚本文件", "Check Format": "脚本格式检查", "Script Loaded Successfully": "脚本加载成功", "Script format check passed": "脚本格式检查通过", "Script format check failed": "脚本格式检查失败", "Failed to Load Script": "加载脚本失败", "Failed to Save Script": "保存脚本失败", "Script saved successfully": "脚本保存成功", "Video Script": "视频脚本", "Video Quality": "视频质量", "Custom prompt for LLM, leave empty to use default prompt": "自定义提示词，留空则使用默认提示词", "Proxy Settings": "代理设置", "HTTP_PROXY": "HTTP 代理", "HTTPs_PROXY": "HTTPS 代理", "Vision Model Settings": "视频分析模型设置", "Vision Model Provider": "视频分析模型提供商", "Vision API Key": "视频分析 API 密钥", "Vision Base URL": "视频分析接口地址", "Vision Model Name": "视频分析模型名称", "Narrato Additional Settings": "Narrato 附加设置", "Narrato API Key": "Narrato API 密钥", "Narrato API URL": "Narrato API 地址", "Text Generation Model Settings": "文案生成模型设置", "LLM Model Name": "大语言模型名称", "LLM Model API Key": "大语言模型 API 密钥", "Text Model Provider": "文案生成模型提供商", "Text API Key": "文案生成 API 密钥", "Text Base URL": "文案生成接口地址", "Text Model Name": "文案生成模型名称", "Account ID": "账户 ID", "Skip the first few seconds": "跳过开头多少秒", "Difference threshold": "差异阈值", "Vision processing batch size": "视觉处理批次大小", "Test Connection": "测试连接", "gemini model is available": "Gemini 模型可用", "gemini model is not available": "Gemini 模型不可用", "NarratoAPI is available": "NarratoAPI 可用", "NarratoAPI is not available": "NarratoAPI 不可用", "Unsupported provider": "不支持的提供商", "0: Keep the audio only, 1: Keep the original sound only, 2: Keep the original sound and audio": "0: 仅保留音频，1: 仅保留原声，2: 保留原声和音频", "Text model is not available": "文案生成模型不可用", "Text model is available": "文案生成模型可用", "Upload Script": "上传脚本", "Upload Script File": "上传脚本文件", "Script Uploaded Successfully": "脚本上传成功", "Invalid JSON format": "无效的JSON格式", "Upload failed": "上传失败", "Video Subtitle Merge": "**合并视频与字幕**", "Upload Video and Subtitle Files": "上传视频和字幕文件", "Matched File Pairs": "已匹配的文件对", "Merge All Files": "合并所有文件", "Merge Function Not Implemented": "合并功能待实现", "No Matched Pairs Found": "未找到匹配的文件对", "Missing Subtitle": "缺少对应的字幕文件, 请使用其他软件完成字幕转录，比如剪映等", "Missing Video": "缺少对应的视频文件", "All Uploaded Files": "所有上传的文件", "Order": "排序序号", "Reorder": "重新排序", "Merging files...": "正在合并文件...", "Merge completed!": "合并完成！", "Download Merged Video": "下载合并后的视频", "Download Merged Subtitle": "下载合并后的字幕", "Error during merge": "合并过程中出错", "Failed to generate merged video.": "生成合并视频失败。", "Failed to generate merged subtitle.": "生成合并字幕失败。", "Error reading merged video file": "读取合并后的视频文件时出错", "Error reading merged subtitle file": "读取合并后的字幕文件时出错", "Error processing video files. Please check if the videos are valid MP4 files.": "处理视频文件时出错。请检查视频是否为有效的MP4文件。", "Error processing subtitle files. Please check if the subtitles are valid SRT files.": "处理字幕文件时出错。请检查字幕是否为有效的SRT文件。", "Preview Merged Video": "预览合并后的视频", "Video Path": "视频路径", "Subtitle Path": "字幕路径", "Enable Proxy": "启用代理", "QwenVL model is available": "QwenVL 模型可用", "QwenVL model is not available": "QwenVL 模型不可用", "System settings": "系统设置", "Clear Cache": "清理缓存", "Cache cleared": "缓存清理完成", "storage directory does not exist": "storage目录不存在", "Failed to clear cache": "清理缓存失败", "Clear frames": "清理关键帧", "Clear clip videos": "清理裁剪视频", "Clear tasks": "清理任务", "Directory cleared": "目录清理完成", "Directory does not exist": "目录不存在", "Failed to clear directory": "清理目录失败", "Subtitle Preview": "字幕预览", "One-Click Transcribe": "一键转录", "Transcribing...": "正在转录中...", "Transcription Complete!": "转录完成！", "Transcription Failed. Please try again.": "转录失败，请重试。", "API rate limit exceeded. Please wait about an hour and try again.": "API 调用次数已达到限制，请等待约一小时后再试。", "Resources exhausted. Please try again later.": "资源已耗尽，请稍后再试。", "Transcription Failed": "转录失败", "Mergeable Files": "可合并文件数", "Subtitle Content": "字幕内容", "Merge Result Preview": "合并结果预览", "Short Generate": "短剧混剪 (高燃剪辑)", "Generate Short Video Script": "AI生成短剧混剪脚本", "Adjust the volume of the original audio": "调整原始音频的音量", "Original Volume": "视频音量", "Auto Generate": "纪录片解说 (画面解说)", "Frame Interval (seconds)": "帧间隔 (秒)", "Frame Interval (seconds) (More keyframes consume more tokens)": "帧间隔 (秒) (更多关键帧消耗更多令牌)", "Batch Size": "批处理大小", "Batch Size (More keyframes consume more tokens)": "批处理大小, 每批处理越少消耗 token 越多", "Short Drama Summary": "短剧解说(仅支持 gemini-2.0-flash)"}}