name-template: 'v$RESOLVED_VERSION'
tag-template: 'v$RESOLVED_VERSION'
categories:
  - title: '🚀 新功能'
    labels:
      - 'feature'
      - 'enhancement'
  - title: '🐛 Bug 修复'
    labels:
      - 'fix'
      - 'bug'
  - title: '🧰 维护'
    labels:
      - 'chore'
      - 'maintenance'
  - title: '📚 文档'
    labels:
      - 'docs'
      - 'documentation'

change-template: '- $TITLE @$AUTHOR (#$NUMBER)'

version-resolver:
  major:
    labels:
      - 'major'
      - 'breaking'
  minor:
    labels:
      - 'minor'
      - 'feature'
  patch:
    labels:
      - 'patch'
      - 'fix'
      - 'bug'
      - 'maintenance'
  default: patch

template: |
  ## 更新内容

  $CHANGES

  ## 贡献者

  $CONTRIBUTORS